<?= $this->extend('templates/system_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-md-4">
            <!-- Profile Card -->
            <div class="card">
                <div class="card-body text-center">
                    <img src="<?= isset($userData['id_photo_filepath']) && !empty($userData['id_photo_filepath']) ? base_url($userData['id_photo_filepath']) : base_url('public/assets/system_images/no-users-img.png') ?>" 
                         class="rounded-circle mb-3" alt="Profile Picture" 
                         style="width: 150px; height: 150px; object-fit: cover;">
                    <h4 class="card-title"><?= $user['name'] ?></h4>
                    <p class="text-muted"><?= ucfirst($user['role']) ?></p>
                    <button type="button" id="changePhotoBtn" class="btn btn-outline-primary btn-sm">Change Photo</button>
                    
                    <!-- Hidden file input for profile photo -->
                    <form id="photoForm" style="display: none;">
                        <input type="file" id="profilePhoto" name="profile_photo" accept="image/*">
                        <?= csrf_field() ?>
                    </form>
                </div>
            </div>
            
            <!-- Additional User Info Card -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">User Information</h5>
                </div>
                <div class="card-body">
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-envelope me-2"></i> Email</span>
                            <span class="text-muted"><?= $userData['email'] ?? 'Not set' ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-phone me-2"></i> Phone</span>
                            <span class="text-muted"><?= $userData['phone'] ?? 'Not set' ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-calendar me-2"></i> Member Since</span>
                            <span class="text-muted"><?= isset($userData['created_at']) ? date('M d, Y', strtotime($userData['created_at'])) : 'Not available' ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-user-clock me-2"></i> Last Login</span>
                            <span class="text-muted"><?= isset($userData['last_login']) ? date('M d, Y H:i', strtotime($userData['last_login'])) : 'Not available' ?></span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="col-md-8">
            <!-- Profile Information -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Profile Information</h5>
                    <div>
                        <span id="profileSaveStatus" class="text-success me-2" style="display:none;">
                            <i class="fas fa-check-circle"></i> Saved
                        </span>
                        <button id="editProfileBtn" type="button" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-pencil-alt"></i> Edit
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <form id="profileForm">
                        <?= csrf_field() ?>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">First Name</label>
                                <input type="text" class="form-control" name="fname" value="<?= $userData['fname'] ?? '' ?>" readonly>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Last Name</label>
                                <input type="text" class="form-control" name="lname" value="<?= $userData['lname'] ?? '' ?>" readonly>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Email</label>
                            <input type="email" class="form-control" name="email" value="<?= $userData['email'] ?? '' ?>" readonly>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Phone Number</label>
                            <input type="tel" class="form-control" name="phone" value="<?= $userData['phone'] ?? '' ?>" readonly>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Address</label>
                            <textarea class="form-control" name="address" rows="3" readonly><?= $userData['address'] ?? '' ?></textarea>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">Date of Birth</label>
                                <input type="date" class="form-control" name="dob" value="<?= $userData['dob'] ?? '' ?>" readonly>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Gender</label>
                                <select class="form-select" name="gender" disabled>
                                    <option value="">Select Gender</option>
                                    <option value="male" <?= (isset($userData['gender']) && $userData['gender'] == 'male') ? 'selected' : '' ?>>Male</option>
                                    <option value="female" <?= (isset($userData['gender']) && $userData['gender'] == 'female') ? 'selected' : '' ?>>Female</option>
                                    <option value="other" <?= (isset($userData['gender']) && $userData['gender'] == 'other') ? 'selected' : '' ?>>Other</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-end">
                            <button type="button" id="cancelEditBtn" class="btn btn-secondary me-2" style="display:none;">Cancel</button>
                            <button type="submit" id="updateProfileBtn" class="btn btn-primary" style="display:none;">Update Profile</button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Change Password -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Change Password</h5>
                </div>
                <div class="card-body">
                    <form id="passwordForm">
                        <?= csrf_field() ?>
                        <div class="mb-3">
                            <label class="form-label">Current Password</label>
                            <input type="password" class="form-control" name="current_password" required>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">New Password</label>
                            <input type="password" class="form-control" name="new_password" required>
                            <div class="form-text">Password must be at least 4 characters long.</div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Confirm New Password</label>
                            <input type="password" class="form-control" name="confirm_password" required>
                        </div>
                        
                        <div class="alert alert-success" id="passwordSuccess" style="display:none;">
                            Password changed successfully!
                        </div>
                        
                        <div class="alert alert-danger" id="passwordError" style="display:none;">
                            Error changing password. Please try again.
                        </div>
                        
                        <button type="submit" id="changePasswordBtn" class="btn btn-primary">Change Password</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
// Function to update CSRF token on the page
function updateCSRFToken(tokenName, tokenValue) {
    // Update all CSRF input fields
    $('input[name="' + tokenName + '"]').val(tokenValue);
    // Update our stored variable for other requests
    csrfHash = tokenValue;
}

$(document).ready(function() {
    // Edit Profile Button
    $('#editProfileBtn').on('click', function() {
        // Enable form fields for editing
        $('#profileForm input, #profileForm textarea, #profileForm select').removeAttr('readonly').prop('disabled', false);
        
        // Show update and cancel buttons
        $('#updateProfileBtn, #cancelEditBtn').show();
        
        // Hide edit button
        $(this).hide();
        
        // Hide save status
        $('#profileSaveStatus').hide();
    });
    
    // Cancel Edit Button
    $('#cancelEditBtn').on('click', function() {
        // Disable form fields
        $('#profileForm input, #profileForm textarea, #profileForm select').attr('readonly', true).prop('disabled', true);
        
        // Reset form to original values
        $('#profileForm')[0].reset();
        $('#profileForm input[name="fname"]').val('<?= $userData['fname'] ?? '' ?>');
        $('#profileForm input[name="lname"]').val('<?= $userData['lname'] ?? '' ?>');
        $('#profileForm input[name="email"]').val('<?= $userData['email'] ?? '' ?>');
        $('#profileForm input[name="phone"]').val('<?= $userData['phone'] ?? '' ?>');
        $('#profileForm textarea[name="address"]').val('<?= $userData['address'] ?? '' ?>');
        $('#profileForm input[name="dob"]').val('<?= $userData['dob'] ?? '' ?>');
        $('#profileForm select[name="gender"]').val('<?= $userData['gender'] ?? '' ?>');
        
        // Hide update and cancel buttons
        $('#updateProfileBtn, #cancelEditBtn').hide();
        
        // Show edit button
        $('#editProfileBtn').show();
    });
    
    // Change Photo Button
    $('#changePhotoBtn').on('click', function() {
        $('#profilePhoto').click();
    });
    
    // Upload profile photo when file is selected
    $('#profilePhoto').on('change', function() {
        if (this.files && this.files[0]) {
            // Create new FormData
            const formData = new FormData();
            
            // Get CSRF token values
            var csrfName = '<?= csrf_token() ?>';
            var csrfHash = '<?= csrf_hash() ?>';
            
            console.log('CSRF Name:', csrfName);
            console.log('CSRF Hash:', csrfHash);
            
            // Add the file and CSRF token to FormData
            formData.append('profile_photo', this.files[0]);
            formData.append(csrfName, csrfHash);
            
            $.ajax({
                url: '<?= base_url('dashboard/update-profile-photo') ?>',
                type: 'POST',
                data: formData,
                contentType: false,
                processData: false,
                dataType: 'json',
                cache: false,
                beforeSend: function(xhr) {
                    // Show loading state on button
                    $('#changePhotoBtn').html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Uploading...').prop('disabled', true);
                },
                success: function(response) {
                    // Reset button state
                    $('#changePhotoBtn').html('Change Photo').prop('disabled', false);
                    
                    if(response.success) {
                        // Update the displayed image
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            $('img[alt="Profile Picture"]').attr('src', e.target.result);
                        }
                        reader.readAsDataURL($('#profilePhoto')[0].files[0]);
                        
                        // Update CSRF token if provided in the response
                        if (response.csrf_token_name && response.csrf_token_value) {
                            updateCSRFToken(response.csrf_token_name, response.csrf_token_value);
                        }
                        
                        // Show success message with Toastr
                        toastr.success(response.message || 'Profile photo updated successfully');
                    } else {
                        // Show error message with Toastr
                        toastr.error(response.message || 'Error updating profile photo');
                    }
                },
                error: function(xhr, status, error) {
                    // Reset button state
                    $('#changePhotoBtn').html('Change Photo').prop('disabled', false);
                    
                    console.error('Upload error:', xhr.responseText);
                    let errorMsg = 'Error updating profile photo';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMsg = xhr.responseJSON.message;
                    }
                    // Show error message with Toastr
                    toastr.error(errorMsg);
                }
            });
        }
    });
    
    // Profile Form Submission - AJAX update
    $('#profileForm').on('submit', function(e) {
        e.preventDefault();
        
        // Get form data and add CSRF token manually
        var formData = $(this).serialize();
        // Add CSRF token manually to ensure it's included properly
        var csrfName = '<?= csrf_token() ?>';
        var csrfHash = '<?= csrf_hash() ?>';
        
        // Send AJAX request
        $.ajax({
            url: '<?= base_url('dashboard/update-profile') ?>',
            type: 'POST',
            data: formData,
            dataType: 'json',
            beforeSend: function(xhr) {
                // Set CSRF header explicitly
                xhr.setRequestHeader('X-CSRF-TOKEN', $('input[name="' + csrfName + '"]').val());
                
                // Show loading state on button
                $('#updateProfileBtn').html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Saving...').prop('disabled', true);
                
                // Hide any previous messages
                $('#profileSaveStatus').hide();
            },
            success: function(response) {
                // Reset button state
                $('#updateProfileBtn').html('Save Changes').prop('disabled', false);
                
                if(response.success) {
                    // Disable form fields
                    $('#profileForm input, #profileForm textarea, #profileForm select').attr('readonly', true).prop('disabled', true);
                    
                    // Hide update and cancel buttons
                    $('#updateProfileBtn, #cancelEditBtn').hide();
                    
                    // Show edit button
                    $('#editProfileBtn').show();
                    
                    // Update CSRF token if provided in the response
                    if (response.csrf_token_name && response.csrf_token_value) {
                        updateCSRFToken(response.csrf_token_name, response.csrf_token_value);
                    }
                    
                    // Show success message with Toastr
                    toastr.success(response.message || 'Profile updated successfully');
                } else {
                    // Show error message with Toastr
                    toastr.error(response.message || 'Error updating profile');
                }
            },
            error: function(xhr, status, error) {
                // Reset button state
                $('#updateProfileBtn').html('Save Changes').prop('disabled', false);
                
                console.error('Profile update error:', xhr.responseText);
                let errorMsg = 'Error updating profile';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMsg = xhr.responseJSON.message;
                }
                
                // Show error message with Toastr
                toastr.error(errorMsg);
            }
        });
    });
    
    // Password Form Submission
    $('#passwordForm').on('submit', function(e) {
        e.preventDefault();
        
        // Get form data and add CSRF token manually
        var formData = $(this).serialize();
        // Add CSRF token manually to ensure it's included properly
        var csrfName = '<?= csrf_token() ?>';
        var csrfHash = '<?= csrf_hash() ?>';
        
        // Send AJAX request
        $.ajax({
            url: '<?= base_url('dashboard/change-password') ?>',
            type: 'POST',
            data: formData,
            dataType: 'json',
            beforeSend: function(xhr) {
                // Set CSRF header explicitly
                xhr.setRequestHeader('X-CSRF-TOKEN', $('input[name="' + csrfName + '"]').val());
                
                // Show loading state on button
                $('#changePasswordBtn').html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Changing...').prop('disabled', true);
                
                // Hide any previous messages
                $('#passwordChangeStatus').hide();
            },
            success: function(response) {
                // Reset button state
                $('#changePasswordBtn').html('Change Password').prop('disabled', false);
                
                if(response.success) {
                    // Reset form
                    $('#passwordForm')[0].reset();
                    
                    // Update CSRF token if provided in the response
                    if (response.csrf_token_name && response.csrf_token_value) {
                        updateCSRFToken(response.csrf_token_name, response.csrf_token_value);
                    }
                    
                    // Show success message with Toastr
                    toastr.success(response.message || 'Password changed successfully');
                } else {
                    // Show error message with Toastr
                    toastr.error(response.message || 'Error changing password');
                }
            },
            error: function(xhr, status, error) {
                // Reset button state
                $('#changePasswordBtn').html('Change Password').prop('disabled', false);
                
                console.error('Password change error:', xhr.responseText);
                let errorMsg = 'Error changing password';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMsg = xhr.responseJSON.message;
                }
                
                // Show error message with Toastr
                toastr.error(errorMsg);
            }
        });
    });
});
</script>
<?= $this->endSection() ?>