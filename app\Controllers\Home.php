<?php

namespace App\Controllers;

use CodeIgniter\RESTful\ResourceController;
use App\Models\UserModel;
use App\Models\DakoiiUserModel;

/**
 * Home Controller
 *
 * Handles authentication and landing pages for the application
 */
class Home extends ResourceController
{
    /**
     * @var UserModel
     */
    protected $userModel;

    /**
     * @var DakoiiUserModel
     */
    protected $dakoiiUserModel;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->userModel = new UserModel();
        $this->dakoiiUserModel = new DakoiiUserModel();
    }

    /**
     * Display the landing page
     *
     * @return \CodeIgniter\HTTP\Response
     */
    public function index()
    {
        return view('home/home_landing');
    }

    /**
     * Display login page or process login
     *
     * @return \CodeIgniter\HTTP\Response
     */
    public function login()
    {
        if ($this->request->is('post')) {
            return $this->loginProcess();
        }

        // For GET requests, show login page
        return redirect()->to(base_url());
    }

    /**
     * Process login request
     *
     * @return \CodeIgniter\HTTP\Response
     */
    public function loginProcess()
    {
        // This method only handles POST submissions
        if (!$this->request->is('post')) {
            return redirect()->to(base_url());
        }

        $email = $this->request->getPost('email');
        $password = $this->request->getPost('password');
        $remember = $this->request->getPost('remember') ? true : false;

        // Validate inputs
        if (empty($email) || empty($password)) {
            return redirect()->back()->with('error', 'Please enter both email and password');
        }

        // Authenticate user using the model's authenticate method
        $user = $this->userModel->authenticate($email, $password);

        if (!$user) {
            return redirect()->back()->with('error', 'Invalid email or password');
        }

        // Check if user is active
        if (isset($user['user_status']) && $user['user_status'] != 1) {
            return redirect()->back()->with('error', 'Your account is not active. Please contact support.');
        }

        // Set session data
        session()->set([
            'user_id' => $user['id'],
            'email' => $user['email'],
            'role' => $user['role'],
            'fname' => $user['fname'],
            'lname' => $user['lname'],
            'user_name' => $user['fname'] . ' ' . $user['lname'],
            'user_status' => $user['user_status'],
            'id_photo' => $user['id_photo_filepath'],
            'is_evaluator' => $user['is_evaluator'] ?? 0,
            'commodity_id' => $user['commodity_id'] ?? null,
            'logged_in' => true
        ]);

        // Set remember me cookie if requested
        if ($remember) {
            $this->setRememberMeCookie($user['id']);
        }

        // Log successful login
        log_message('info', 'User logged in successfully: ' . $user['email']);

        // Redirect based on role
        return redirect()->to(base_url('dashboard'))->with('success', 'Login successful');
    }

    /**
     * Process user logout
     *
     * @return \CodeIgniter\HTTP\Response
     */
    public function logout()
    {
        session()->destroy();
        return redirect()->to(base_url())->with('message', 'You have been logged out successfully.');
    }

    /**
     * Display the Dakoii login page
     *
     * @return \CodeIgniter\HTTP\Response
     */
    public function dakoii()
    {
        // If already logged in, redirect to dashboard
        if (session()->get('dakoii_logged_in')) {
            return redirect()->to(base_url('dakoii/dashboard'));
        }

        return view('home/home_dakoii');
    }

    /**
     * Process Dakoii login
     *
     * @return \CodeIgniter\HTTP\Response
     */
    public function dakoiiLogin()
    {
        // Only accept POST requests
        if (!$this->request->is('post')) {
            return redirect()->to(base_url('dakoii'))->with('error', 'Invalid request method');
        }

        // Get form inputs
        $username = trim($this->request->getPost('username'));
        $password = trim($this->request->getPost('password'));

        // Enhanced logging for debugging
        log_message('debug', '-------------------------------------');
        log_message('debug', 'Dakoii login request received');
        log_message('debug', 'Username: ' . $username);
        log_message('debug', 'Password length: ' . strlen($password));
        log_message('info', 'Dakoii login attempt for username: ' . $username);

        // Validate required fields
        if (empty($username) || empty($password)) {
            log_message('warning', 'Empty username or password submitted');
            return redirect()->to(base_url('dakoii'))
                ->with('error', 'Username and password are required');
        }

        // Authenticate user
        log_message('debug', 'Calling authenticate method for user: ' . $username);
        $user = $this->dakoiiUserModel->authenticate($username, $password);
        log_message('debug', 'Authentication result: ' . ($user ? 'Success' : 'Failed'));

        if ($user) {
            // Set session data on successful login
            $sessionData = [
                'dakoii_user_id'  => $user['id'],
                'dakoii_name'     => $user['name'],
                'dakoii_username' => $user['username'],
                'dakoii_role'     => $user['role'],
                'dakoii_orgcode'  => $user['orgcode'],
                'dakoii_logged_in'=> true
            ];

            session()->set($sessionData);

            // Verify session data was set correctly
            log_message('debug', 'Session data set. Logged in: ' . (session()->get('dakoii_logged_in') ? 'Yes' : 'No'));
            log_message('info', 'Dakoii user logged in successfully: ' . $username);

            // Redirect to dashboard
            return redirect()->to(base_url('dakoii/dashboard'))
                ->with('success', 'Welcome back, ' . $user['name']);
        }

        // Handle failed login
        log_message('warning', 'Failed Dakoii login attempt for username: ' . $username);

        // Let's check what's in the database for this username
        try {
            $checkUser = $this->dakoiiUserModel->where('username', $username)->first();
            if ($checkUser) {
                log_message('debug', 'User exists but authentication failed. Status: ' .
                    ($checkUser['dakoii_user_status'] ?? 'undefined'));
            } else {
                log_message('debug', 'User does not exist in database');
            }
        } catch(\Exception $e) {
            log_message('error', 'Error checking user: ' . $e->getMessage());
        }

        return redirect()->to(base_url('dakoii'))
            ->with('error', 'Invalid username or password');
    }

    /**
     * Set remember-me cookie
     *
     * @param int $userId
     * @return void
     */
    private function setRememberMeCookie($userId)
    {
        $token = bin2hex(random_bytes(32));
        $expiry = time() + (30 * 24 * 60 * 60); // 30 days

        // In a real application, you should store this token in a database
        // For simplicity, we're just storing the user ID in the cookie
        set_cookie(
            'remember_token',
            $userId,
            $expiry
        );
    }

    /**
     * Check if user is logged in
     *
     * @return bool
     */
    private function isLoggedIn()
    {
        return session()->get('dakoii_logged_in') === true;
    }
}
