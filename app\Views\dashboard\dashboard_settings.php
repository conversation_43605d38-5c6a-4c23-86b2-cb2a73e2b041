<?= $this->extend('templates/system_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-md-6">
            <!-- Notification Settings -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Notification Settings</h5>
                </div>
                <div class="card-body">
                    <form id="notificationForm">
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="email_notifications" id="emailNotifications">
                                <label class="form-check-label" for="emailNotifications">Email Notifications</label>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="sms_notifications" id="smsNotifications">
                                <label class="form-check-label" for="smsNotifications">SMS Notifications</label>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="task_reminders" id="taskReminders">
                                <label class="form-check-label" for="taskReminders">Task Reminders</label>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="system_updates" id="systemUpdates">
                                <label class="form-check-label" for="systemUpdates">System Updates</label>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">Save Notification Settings</button>
                    </form>
                </div>
            </div>
            
            <!-- Privacy Settings -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Privacy Settings</h5>
                </div>
                <div class="card-body">
                    <form id="privacyForm">
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="profile_visibility" id="profileVisibility">
                                <label class="form-check-label" for="profileVisibility">Make Profile Public</label>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="activity_visibility" id="activityVisibility">
                                <label class="form-check-label" for="activityVisibility">Show Activity Status</label>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">Save Privacy Settings</button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <!-- Language Settings -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Language Settings</h5>
                </div>
                <div class="card-body">
                    <form id="languageForm">
                        <div class="mb-3">
                            <label class="form-label">System Language</label>
                            <select class="form-select" name="system_language">
                                <option value="en">English</option>
                                <option value="es">Spanish</option>
                                <option value="fr">French</option>
                                <option value="de">German</option>
                                <option value="it">Italian</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Date Format</label>
                            <select class="form-select" name="date_format">
                                <option value="mm/dd/yyyy">MM/DD/YYYY</option>
                                <option value="dd/mm/yyyy">DD/MM/YYYY</option>
                                <option value="yyyy-mm-dd">YYYY-MM-DD</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Time Zone</label>
                            <select class="form-select" name="timezone">
                                <option value="UTC">UTC</option>
                                <option value="EST">Eastern Time</option>
                                <option value="CST">Central Time</option>
                                <option value="MST">Mountain Time</option>
                                <option value="PST">Pacific Time</option>
                            </select>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">Save Language Settings</button>
                    </form>
                </div>
            </div>
            
            <!-- Theme Settings -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Theme Settings</h5>
                </div>
                <div class="card-body">
                    <form id="themeForm">
                        <div class="mb-3">
                            <label class="form-label">Color Theme</label>
                            <select class="form-select" name="color_theme">
                                <option value="light">Light</option>
                                <option value="dark">Dark</option>
                                <option value="auto">Auto (System)</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Font Size</label>
                            <select class="form-select" name="font_size">
                                <option value="small">Small</option>
                                <option value="medium" selected>Medium</option>
                                <option value="large">Large</option>
                            </select>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">Save Theme Settings</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Notification Settings Form
    $('#notificationForm').on('submit', function(e) {
        e.preventDefault();
        $.ajax({
            url: '<?= base_url('dashboard/update-notifications') ?>',
            type: 'POST',
            data: $(this).serialize(),
            success: function(response) {
                if(response.success) {
                    alert('Notification settings updated successfully');
                } else {
                    alert('Error updating notification settings');
                }
            },
            error: function() {
                alert('Error updating notification settings');
            }
        });
    });
    
    // Privacy Settings Form
    $('#privacyForm').on('submit', function(e) {
        e.preventDefault();
        $.ajax({
            url: '<?= base_url('dashboard/update-privacy') ?>',
            type: 'POST',
            data: $(this).serialize(),
            success: function(response) {
                if(response.success) {
                    alert('Privacy settings updated successfully');
                } else {
                    alert('Error updating privacy settings');
                }
            },
            error: function() {
                alert('Error updating privacy settings');
            }
        });
    });
    
    // Language Settings Form
    $('#languageForm').on('submit', function(e) {
        e.preventDefault();
        $.ajax({
            url: '<?= base_url('dashboard/update-language') ?>',
            type: 'POST',
            data: $(this).serialize(),
            success: function(response) {
                if(response.success) {
                    alert('Language settings updated successfully');
                } else {
                    alert('Error updating language settings');
                }
            },
            error: function() {
                alert('Error updating language settings');
            }
        });
    });
    
    // Theme Settings Form
    $('#themeForm').on('submit', function(e) {
        e.preventDefault();
        $.ajax({
            url: '<?= base_url('dashboard/update-theme') ?>',
            type: 'POST',
            data: $(this).serialize(),
            success: function(response) {
                if(response.success) {
                    alert('Theme settings updated successfully');
                } else {
                    alert('Error updating theme settings');
                }
            },
            error: function() {
                alert('Error updating theme settings');
            }
        });
    });
});
</script>
<?= $this->endSection() ?> 